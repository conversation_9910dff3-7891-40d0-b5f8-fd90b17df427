# Teams設定データの流れと型の詳細分析

## 概要

useUserChatsAndChannelsAccessorからデータを取得し、TeamsSettingModalでデータを表示、useTeamsChatsApiAccessorで保存する一連の流れについて、PartitionKey（userId）とRowKey（chatId/channelId）の設定を含めて詳細に分析した結果を記載します。

**結論**: 特に問題なし。PartitionKey（userId）とRowKey設定は適切に実装されています。

## 1. データ取得フロー

### フロー図
```
TeamsSettingModal
    ↓
useUserChatsAndChannelsAccessor
    ↓
Microsoft Graph API
    ├── fetchUserChatsImpl (/me/chats)
    └── fetchUserTeamsAndChannelsImpl (/me/joinedTeams)
    ↓
convertToUserChatItems
    ↓
IUserChatItem[]
    ↓
TeamsSettingModal表示
```

### 取得される型

#### IUserChatItem（表示用統合型）
```typescript
interface IUserChatItem {
  id: string;                    // chatId または channelId
  name: string;                  // 表示名
  type: 'チャット' | 'チャネル';    // アイテムタイプ
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'TeamsChannel';
  teamId?: string;               // チャネルの場合のチームID
}
```

#### IUserChat（Graph APIレスポンス - チャット）
```typescript
interface IUserChat {
  id: string;
  topic: string | null;
  chatType: string;
  members?: {
    displayName: string;
    id: string;
  }[];
}
```

#### IUserTeam（Graph APIレスポンス - チーム）
```typescript
interface IUserTeam {
  id: string;
  displayName: string;
  channels?: IUserChannel[];
}

interface IUserChannel {
  id: string;
  displayName: string;
  membershipType: string;
}
```

## 2. データ保存フロー

### フロー図（キュー方式）
```
TeamsSettingModal.handleSave
    ↓
選択されたアイテムをTeamsChatsItemに変換
    ↓
キューを作成（直列処理用）
    ↓
各アイテムを順次処理（エラー時は停止）
    ↓
ITeamsChatsRequest作成
    ↓
useTeamsChatsApiAccessor.postTeamsChatsApi
    ↓
getUniqueNameByToken（JWTからuserId抽出）
    ↓
API POST /users/{userId}/teams-chats
    ↓
TeamsChatsManager.CreateAsync
    ↓
TeamsChatsTableEntity.FromTeamsChats
    ↓
Azure Table Storage保存
```

### キュー処理の特徴
- **直列処理**: アイテムを一つずつ順番に処理
- **エラー時停止**: 一つでも失敗した場合は残りの処理を停止
- **進捗管理**: 処理済み件数と残り件数を管理
- **ログ出力**: 各アイテムの処理状況をコンソールに出力

### 保存される型

#### ITeamsChatsRequest（フロントエンド送信）
```typescript
interface ITeamsChatsRequest {
  countId: number;                                           // ユーザー選択順序（1-10）
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'TeamsChannel';
  
  // チャット用フィールド
  chatId?: string;                                          // チャットの場合のID
  
  // チャネル用フィールド
  teamId?: string;                                          // チームID
  channelId?: string;                                       // チャネルの場合のID
}
```

#### TeamsChats（ビジネスエンティティ）
```csharp
class TeamsChats : EntityBase, IPrimaryKey {
  string? Id { get; set; }           // GUID（自動生成）
  string? ChatId { get; set; }       // チャットID
  string? ChannelId { get; set; }    // チャネルID
  string? ChatType { get; set; }     // チャットタイプ
  string? Name { get; set; }         // 名前
  string? UserId { get; set; }       // ユーザーID
  DateTime? CreatedAt { get; set; }  // 作成日時
  DateTime? UpdatedAt { get; set; }  // 更新日時
}
```

#### TeamsChatsTableEntity（Table Storage）
```csharp
class TeamsChatsTableEntity : ITableEntity {
  string PartitionKey { get; set; }  // = userId（自動設定）
  string RowKey { get; set; }        // = Id（GUID、自動生成）
  string? ChatId { get; set; }       // チャットID
  string? ChannelId { get; set; }    // チャネルID
  string? ChatType { get; set; }     // チャットタイプ
  string? Name { get; set; }         // 名前
  DateTime? CreatedAt { get; set; }  // 作成日時
  DateTime? UpdatedAt { get; set; }  // 更新日時
}
```

## 3. キー設定の詳細

### PartitionKey（userId）の設定プロセス

1. **JWTトークン解析**
   ```typescript
   // getUniqueNameByToken関数
   const [token, uId] = await getUniqueNameByToken(tokenProvider);
   // JWTのpayload.oidを抽出してuserIdとして使用
   ```

2. **API URL構築**
   ```typescript
   // createPostTeamsChatsUrl関数
   return `${PREFIX}/users/${userId}/teams-chats`;
   ```

3. **Table Storage設定**
   ```csharp
   // TeamsChatsTableEntity.FromTeamsChats
   PartitionKey = teamsChats.UserId ?? string.Empty,
   ```

### RowKey設定

**実装**: `RowKey = Id`（GUID、自動生成）
```csharp
RowKey = teamsChats.Id ?? string.Empty,
```

**注意**: コメントには「RowKey = chatId/channelId」と記載されていますが、実際の実装ではGUIDを使用。chatId/channelIdは別フィールドとして保存されます。

## 4. データマッピング例

### チャット選択時のマッピング
```typescript
// TeamsSettingModal.handleSave
const request: ITeamsChatsRequest = {
  countId: index + 1,              // 選択順序
  chatType: item.chatType,         // 'oneOnOne' | 'group' | 'meeting'
  chatId: item.id,                 // チャットID
  // teamId, channelIdは未設定
};
```

### チャネル選択時のマッピング
```typescript
// TeamsSettingModal.handleSave
const request: ITeamsChatsRequest = {
  countId: index + 1,              // 選択順序
  chatType: 'TeamsChannel',        // 固定値
  teamId: item.teamId,             // チームID
  channelId: item.id,              // チャネルID
  // chatIdは未設定
};
```

## 5. バリデーション

### フロントエンド
- countIdの範囲チェック（1-10）
- chatTypeの必須チェック
- 選択上限チェック（最大10件）

### バックエンド
- 必須項目チェック（Name, ChatType, UserId）
- チャットタイプ別必須項目チェック
  - チャット: ChatId必須
  - チャネル: ChannelId必須

## 6. 確認結果

✅ **問題なし**: 以下の点で適切に実装されています

1. **PartitionKey（userId）**: JWTトークンから正しく抽出・設定
2. **データ保存**: chatId/channelIdは適切に個別フィールドとして保存
3. **型変換**: フロントエンドからバックエンドまで一貫した型変換
4. **バリデーション**: 適切なデータ検証が実装済み
5. **エラーハンドリング**: 各段階で適切なエラー処理

唯一の注意点は、コメントと実装の差異（RowKey設定）ですが、機能的には問題ありません。

## 7. キュー機能の実装詳細

### useTeamsChatsQueue
TeamsChats用のキュー機能を提供するカスタムフック。お気に入り機能と同様のパターンで実装。

#### 主要機能
1. **IUserChatItemからITeamsChatsItemへの変換**
2. **キューを使った直列API呼び出し**
3. **エラーハンドリングと進捗管理**

#### 型定義
```typescript
interface ITeamsChatsItem {
  id: string;                    // chatId または channelId
  name: string;                  // 表示名
  type: 'チャット' | 'チャネル';    // アイテムタイプ
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'TeamsChannel';
  teamId?: string;               // チームID（チャネルの場合）
  countId: number;               // 選択順序（1から開始）
}

interface ITeamsChatsQueue {
  date: Date;                    // キュー作成日時
  data: ITeamsChatsItem;         // キューデータ
}
```

#### 使用方法
```typescript
const { addMultipleTeamsChats, convertUserChatItems } = useTeamsChatsQueue(
  postTeamsChatsApi,
  eventReporter,
);

// 複数アイテムの一括保存
await addMultipleTeamsChats(teamsChatsItems);
```

### エラー処理
- **MISSING_PARAMS**: 必須パラメータ不足
- **QUEUE_SEND_ERROR**: キュー送信エラー
- **IS_OFFLINE_OR_SOMETHING_WRONG**: ネットワークエラー

### 今後の拡張予定
1. **IndexedDBを使った永続化**: オフライン対応
2. **バックグラウンド同期**: 定期的な再送信
3. **削除機能**: 設定済みアイテムの削除
