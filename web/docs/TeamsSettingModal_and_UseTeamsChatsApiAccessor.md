# TeamsSettingModal と UseTeamsChatsApiAccessor 機能仕様書

## 概要

TeamsSettingModalとUseTeamsChatsApiAccessorは、Microsoft Teamsのチャットやチャネルを検索対象として設定・管理するための機能です。ユーザーがTeamsの特定のチャットやチャネルを選択し、それらをシステムに登録することで、検索機能の対象範囲を制御できます。

## 機能詳細

### TeamsSettingModal

#### 概要
Teamsのチャットとチャネルを選択・設定するためのモーダルダイアログコンポーネントです。

#### 主要機能
1. **チャット・チャネル一覧表示**: Microsoft Graph APIを通じてユーザーが参加しているチャットとチャネルを取得・表示
2. **検索・フィルタリング**: チャット/チャネル名やIDによる絞り込み検索
3. **複数選択**: チェックボックス形式での複数アイテム選択
4. **設定保存**: 選択したアイテムをバックエンドAPIに送信して保存

#### プロパティ
```typescript
interface ISimpleModalProps {
  className?: string;                                    // CSSクラス名
  open?: boolean;                                        // モーダル表示状態
  onClose: () => void;                                   // 閉じる時のコールバック
  useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType; // API操作用フック
}
```

#### 状態管理
- `allChatItems`: 取得した全チャット・チャネルアイテム
- `filteredChatItems`: フィルタリング後のアイテム
- `selectedItems`: 選択されたアイテムのIDセット
- `itemId`: 検索フィルター用の入力値
- `chatId`: チャットID入力フィールドの値
- `isLoading`: データ読み込み状態
- `error`: エラー状態

#### 主要メソッド
- `handleItemToggle`: アイテムの選択状態切り替え
- `handleChatIdChange`: チャットID入力の変更処理
- `handleKeyDown`: キーボード操作対応（Enter/Space）

### UseTeamsChatsApiAccessor

#### 概要
Teams設定をバックエンドAPIに保存するためのカスタムフックです。

#### 主要機能
1. **Teams設定保存**: 選択されたチャット・チャネル情報をAPIに送信
2. **認証管理**: トークンプロバイダーを使用した認証処理
3. **エラーハンドリング**: API呼び出し時のエラー処理とレポート
4. **パラメータ検証**: リクエストデータの妥当性チェック

#### 型定義
```typescript
interface ITeamsChatsRequest {
  chatOrChannelId?: string;           // チャットまたはチャネルのID
  chatType: 'チャット' | 'チャネル';    // アイテムタイプ
}

type PostTeamsChatsApi = (request: ITeamsChatsRequest) => Promise<void>;

type UseTeamsChatsApiReturnType = {
  postTeamsChatsApi: PostTeamsChatsApi | undefined;
}
```

#### エラー定数
- `MISSING_PARAMS`: 必須パラメータ不足
- `NO_TOKENS`: 認証トークン未提供
- `IS_OFFLINE_OR_SOMETHING_WRONG`: ネットワークエラーまたはAPI障害

## アーキテクチャ

### フロントエンド構成
```
TeamsSettingModal
├── useUserChatsAndChannelsAccessor (Microsoft Graph API)
├── useTeamsChatsApiAccessor (バックエンドAPI)
└── useComponentInitUtility (認証・初期化)
```

### バックエンドAPI構成
```
TeamsChatRequestController
├── POST /users/{userId}/teams-chats (設定登録)
├── GET /users/{userId}/teams-chats (設定取得)
└── DELETE /users/{userId}/teams-chats/{id} (設定削除)
```

### データフロー
1. **初期化**: モーダル表示時にMicrosoft Graph APIからチャット・チャネル一覧を取得
2. **表示**: 取得したデータをフィルタリング可能なリストとして表示
3. **選択**: ユーザーが対象アイテムを複数選択
4. **保存**: 選択されたアイテムをバックエンドAPIに送信して永続化

## データベース設計

### TeamsChatsテーブル
| カラム名 | 型 | 説明 |
|---------|---|------|
| Id | string | 主キー |
| ChatId | string | チャットID（チャットタイプの場合） |
| ChannelId | string | チャネルID（チャネルタイプの場合） |
| ChatType | string | 'チャット' または 'チャネル' |
| Name | string | 表示名 |
| UserId | string | 設定を行ったユーザーID |
| CreatedAt | DateTime | 作成日時 |
| UpdatedAt | DateTime | 更新日時 |

## 使用箇所

### SplitViewContainer
TeamsSettingModalは`SplitViewContainer`コンポーネント内で使用されており、検索機能の設定画面として機能します。

```typescript
<TeamsSettingModal
  className="home-simple-modal-content"
  open={modalOpen}
  onClose={hideModal}
  useTeamsChatsApiAccessorReturn={useTeamsChatsApiAccessorReturn}
/>
```

## 技術的な特徴

### 認証・セキュリティ
- Microsoft Graph APIアクセス用のトークンプロバイダー使用
- ユーザーID検証によるアクセス制御
- CORS対応とHTTPS通信

### パフォーマンス最適化
- React.useCallback によるメモ化
- 検索フィルタリングの最適化
- 必要時のみAPI呼び出し

### アクセシビリティ
- キーボード操作対応（Enter/Space）
- ARIA属性による支援技術対応
- フォーカス管理

## 今後の開発予定

### 未実装機能（TODOコメントより）
1. **ITeamsChatsRequest型の確定**: chatTypeの詳細仕様確認
2. **パラメータ検証の完成**: name フィールドの必須チェック実装
3. **保存機能の実装**: 選択されたアイテムの実際の保存処理
4. **テストコードの完成**: 現在コメントアウトされているテストの実装

### 改善予定
- エラーハンドリングの強化
- ユーザビリティの向上
- パフォーマンス最適化
- 国際化対応
