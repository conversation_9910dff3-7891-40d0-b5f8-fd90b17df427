import * as React from 'react';
import { IDBPDatabase, openDB } from 'idb';
import {
  BOOKMARK_QUEUE_STORE,
  BOOKMARKS_STORE,
  IGeraniumAttaneDB,
  INDEXED_DB_NAME,
  INDEX_DB_VERSION,
  SEARCH_RESULTS_CACHE,
  TEAMS_CHATS_STORE,
  TEAMS_CHATS_QUEUE_STORE,
} from '../../types/IGeraniumAttaneDB';

/**
 * Provide IndexedDB accessor
 */
const useIndexedDbAccessor = (): [(() => Promise<IDBPDatabase<IGeraniumAttaneDB>>) | undefined] => {
  // リポジトリの初期化後にtrue
  const [isInitialized, setIsInitialized] = React.useState(false);

  /**
   * Initialization
   */
  React.useEffect(() => {
    (async () => {
      const db = await openDB<IGeraniumAttaneDB>(INDEXED_DB_NAME, INDEX_DB_VERSION, {
        upgrade(d, oldVersion) {
          // 新規作成時 または CURRENT_DB_VERSIONよりも古いバージョンのデータベースが存在するときに実行される
          // 既にCURRENT_DB_VERSIONのデータベースが存在する場合は何もしない

          if (oldVersion < 1) {
            d.createObjectStore(BOOKMARKS_STORE);
            d.createObjectStore(BOOKMARK_QUEUE_STORE);
          }
          if (oldVersion < 2) {
            d.createObjectStore(SEARCH_RESULTS_CACHE);
          }
          if (oldVersion < 3) {
            d.deleteObjectStore(BOOKMARKS_STORE);
            d.deleteObjectStore(BOOKMARK_QUEUE_STORE);
            d.deleteObjectStore(SEARCH_RESULTS_CACHE);

            d.createObjectStore(BOOKMARKS_STORE);
            d.createObjectStore(BOOKMARK_QUEUE_STORE);
            d.createObjectStore(SEARCH_RESULTS_CACHE);
          }
          if (oldVersion < 4) {
            d.createObjectStore(TEAMS_CHATS_STORE);
            d.createObjectStore(TEAMS_CHATS_QUEUE_STORE);
          }
        },
      });
      db.close();
      setIsInitialized(true);
    })();
  }, []);

  const odb = React.useCallback(
    () => openDB<IGeraniumAttaneDB>(INDEXED_DB_NAME, INDEX_DB_VERSION),
    [],
  );

  return [isInitialized ? odb : undefined];
};

export default useIndexedDbAccessor;
