// import { renderHook, act } from '@testing-library/react-hooks';
// import useUserChatsAndChannelsAccessor from './useUserChatsAndChannelsAccessor';
// import { WeakTokenProvider } from '../../types/TokenProvider';
// import { UseGraphApiError } from './useGraphApiAccessor';

// // Microsoft Graph Clientをモック
// jest.mock('./useGraphApiAccessor', () => ({
//   UseGraphApiError: {
//     TOKEN_PROVIDER_NOT_AVAILABLE: 'TOKEN_PROVIDER_NOT_AVAILABLE',
//   },
//   initGraphClient: jest.fn(),
// }));

// const mockInitGraphClient = require('./useGraphApiAccessor').initGraphClient;

// describe('useUserChatsAndChannelsAccessor', () => {
//   const mockTokenProvider: WeakTokenProvider = jest.fn().mockResolvedValue('mock-token');

//   beforeEach(() => {
//     jest.clearAllMocks();
//   });

//   describe('初期化テスト', () => {
//     it('tokenProviderが提供されている場合、fetchUserChatsAndChannelsが定義される', () => {
//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       expect(result.current.fetchUserChatsAndChannels).toBeDefined();
//       expect(result.current.isLoading).toBe(false);
//       expect(result.current.error).toBe(null);
//     });

//     it('tokenProviderが未定義の場合、fetchUserChatsAndChannelsが未定義になる', () => {
//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(undefined));

//       expect(result.current.fetchUserChatsAndChannels).toBeUndefined();
//       expect(result.current.isLoading).toBe(false);
//       expect(result.current.error).toBe(null);
//     });
//   });

//   describe('データ取得テスト', () => {
//     const mockChatsResponse = {
//       value: [
//         {
//           id: 'chat1',
//           topic: 'テストチャット1',
//           chatType: 'oneOnOne',
//           members: [
//             { displayName: 'ユーザー1', id: 'user1' },
//             { displayName: 'ユーザー2', id: 'user2' },
//           ],
//         },
//         {
//           id: 'chat2',
//           topic: null,
//           chatType: 'group',
//           members: [
//             { displayName: 'ユーザー3', id: 'user3' },
//             { displayName: 'ユーザー4', id: 'user4' },
//           ],
//         },
//       ],
//     };

//     const mockTeamsResponse = {
//       value: [
//         {
//           id: 'team1',
//           displayName: 'テストチーム1',
//         },
//       ],
//     };

//     const mockChannelsResponse = {
//       value: [
//         {
//           id: 'channel1',
//           displayName: 'テストチャネル1',
//           membershipType: 'standard',
//         },
//         {
//           id: 'channel2',
//           displayName: 'テストチャネル2',
//           membershipType: 'private',
//         },
//       ],
//     };

//     it('チャットとチャネルを正常に取得できる', async () => {
//       const mockClient = {
//         api: jest.fn().mockReturnThis(),
//         expand: jest.fn().mockReturnThis(),
//         get: jest.fn()
//           .mockResolvedValueOnce(mockChatsResponse) // /me/chats
//           .mockResolvedValueOnce(mockTeamsResponse) // /me/joinedTeams
//           .mockResolvedValueOnce(mockChannelsResponse), // /teams/{teamId}/channels
//       };

//       mockInitGraphClient.mockReturnValue(mockClient);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       let fetchResult: any;
//       await act(async () => {
//         fetchResult = await result.current.fetchUserChatsAndChannels!();
//       });

//       expect(fetchResult).toHaveLength(4); // 2チャット + 2チャネル

//       // チャットの確認
//       expect(fetchResult[0]).toEqual({
//         id: 'chat1',
//         name: 'テストチャット1',
//         type: 'チャット',
//         isBookmarked: false,
//       });

//       expect(fetchResult[1]).toEqual({
//         id: 'chat2',
//         name: 'ユーザー3, ユーザー4',
//         type: 'チャット',
//         isBookmarked: false,
//       });

//       // チャネルの確認
//       expect(fetchResult[2]).toEqual({
//         id: 'channel1',
//         name: 'テストチーム1 - テストチャネル1',
//         type: 'チャネル',
//         isBookmarked: false,
//       });

//       expect(fetchResult[3]).toEqual({
//         id: 'channel2',
//         name: 'テストチーム1 - テストチャネル2',
//         type: 'チャネル',
//         isBookmarked: false,
//       });
//     });

//     it('tokenProviderが利用できない場合、エラーを投げる', async () => {
//       mockInitGraphClient.mockReturnValue(null);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       await act(async () => {
//         try {
//           await result.current.fetchUserChatsAndChannels!();
//         } catch (error) {
//           expect(error).toEqual(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
//         }
//       });

//       expect(result.current.error).toBe(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE);
//     });

//     it('API呼び出しでエラーが発生した場合、エラー状態を設定する', async () => {
//       const mockClient = {
//         api: jest.fn().mockReturnThis(),
//         expand: jest.fn().mockReturnThis(),
//         get: jest.fn().mockRejectedValue(new Error('API Error')),
//       };

//       mockInitGraphClient.mockReturnValue(mockClient);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       await act(async () => {
//         try {
//           await result.current.fetchUserChatsAndChannels!();
//         } catch (error) {
//           expect(error).toEqual(new Error('API Error'));
//         }
//       });

//       expect(result.current.error).toBe('API Error');
//     });
//   });

//   describe('ローディング状態テスト', () => {
//     it('データ取得中はisLoadingがtrueになる', async () => {
//       const mockClient = {
//         api: jest.fn().mockReturnThis(),
//         expand: jest.fn().mockReturnThis(),
//         get: jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100))),
//       };

//       mockInitGraphClient.mockReturnValue(mockClient);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       act(() => {
//         result.current.fetchUserChatsAndChannels!();
//       });

//       expect(result.current.isLoading).toBe(true);
//     });
//   });
// });
