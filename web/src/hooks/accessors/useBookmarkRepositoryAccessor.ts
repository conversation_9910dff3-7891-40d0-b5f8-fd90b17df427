import dayjs from 'dayjs';
import * as React from 'react';
import { IBookmarkDict } from '../../types/IBookmarkDict';
import {
  BOOKMARK_QUEUE_STORE,
  BOOKMARKS_STORE,
  BookmarkQueueType, INDEX_DB_VERSION, DbProvider, IRepositoryBookmarkQueue,
} from '../../types/IGeraniumAttaneDB';
import { sortArrayByKey } from '../../utilities/array';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

export type RetrieveBookmarks = () => Promise<ISplitViewListSingle[]>;
export type AddBookmark = (item: ISplitViewListSingle) => Promise<string>;
export type UpdateBookmark = (item: ISplitViewListSingle) => Promise<string | undefined>;
export type DeleteBookmark = (itemId: string) => Promise<void>;
export type ReplaceBookmarks = (items: ISplitViewListSingle[]) => Promise<void>;
export type GetBookmarkQueues = () => Promise<IRepositoryBookmarkQueue[]>;
export type AddBookmarkQueue =
  (data: ISplitViewListSingle, type: BookmarkQueueType) => Promise<void>;
export type DeleteBookmarkQueue = (spoId: string) => Promise<void>;
export type ReplaceBookmarkQueues = (items: IRepositoryBookmarkQueue[]) => Promise<void>;

export type UseBookmarkRepositoryReturnType = {
  addBookmark: AddBookmark | undefined,
  updateBookmark: UpdateBookmark | undefined,
  deleteBookmark: DeleteBookmark | undefined,
  replaceBookmarks: ReplaceBookmarks | undefined,
  getBookmarkQueues: GetBookmarkQueues | undefined,
  addBookmarkQueue: AddBookmarkQueue | undefined,
  deleteBookmarkQueue: DeleteBookmarkQueue | undefined,
  replaceBookmarkQueues: ReplaceBookmarkQueues | undefined,
  isTransactionPending: boolean,
  allBookmarks: ISplitViewListSingle[],
  bookmarkDict: IBookmarkDict,
}

/**
 * ISplitViewListSingleを新規登録用に加工する
 * @param item
 */
function createNewBookmarkReposEntry(item: ISplitViewListSingle): ISplitViewListSingle {
  const current = new Date().toISOString();
  return {
    ...item,
    reposCreatedDate: current,
    reposUpdatedDate: current,
  };
}

/**
 * ISplitViewListSingleを更新用に加工する
 * @param original
 * @param toBe
 */
function createExistingBookmarkReposEntry(
  original: ISplitViewListSingle,
  toBe: ISplitViewListSingle,
): ISplitViewListSingle {
  const current = new Date().toISOString();
  return {
    ...toBe,
    reposCreatedDate: original.reposCreatedDate,
    reposUpdatedDate: current,
  };
}

// 保存可能な最大件数
export const MAX_BOOKMARKS_COUNT = 200;

// DBバージョン
export const REPOS_CURRENT_DB_VERSION = INDEX_DB_VERSION;

export const UseRepositoryError = {
  MAX_BOOKMARKS: 'MAX_BOOKMARKS',
};

const UseBookmarkRepositoryAccessor = (openDB?: DbProvider): UseBookmarkRepositoryReturnType => {
  const isUnmounted = React.useRef(false);

  // リポジトリの初期化後にtrue
  const [isInitialized, setIsInitialized] = React.useState(false);
  // トランザクション処理中はtrue
  const [isTransactionPending, setIsTransactionPending] = React.useState(false);
  // お気に入り一覧
  const [allBookmarks, setAllBookmarks] = React.useState<ISplitViewListSingle[]>([]);

  /**
   * 指定したStoreのデータを全て取得する
   */
  const retrieveBookmarks: RetrieveBookmarks = React.useCallback(async () => {
    if (!openDB) return [];
    const db = await openDB();
    try {
      return db
        .getAll<typeof BOOKMARKS_STORE>(BOOKMARKS_STORE)
        .then((results) => sortArrayByKey(results, 'reposCreatedDate', 'desc'));
    } finally {
      db.close();
    }
  }, [openDB]);

  /**
   * IndexedDBの内容で画面用データを再生成する
   */
  const renewAllBookmarks = React.useCallback(async () => {
    const bookmarks = await retrieveBookmarks().catch(() => []);
    if (isUnmounted.current) return;
    setAllBookmarks(bookmarks);
  }, [retrieveBookmarks]);

  /**
   * 初期化処理
   */
  React.useEffect(() => {
    if (!openDB) return;
    (async () => {
      // お気に入りを全件取得して保存
      await renewAllBookmarks();
      setIsInitialized(true);
    })();
  }, [openDB, renewAllBookmarks]);

  React.useEffect(() => () => { isUnmounted.current = true; }, []);

  /**
   * 最新のお気に入り一覧から生成したID辞書
   */
  const bookmarkDict = React.useMemo(() => allBookmarks
    .map((val) => val?.id ?? '')
    .filter((val) => !!val)
    .reduce<IBookmarkDict>((result, current) => ({
      ...result,
      [current]: true,
    }), {}), [allBookmarks]);

  /**
   * お気に入りを全件置き換える
   * itemsが0件の場合は全消去を行う
   */
  const replaceBookmarks: ReplaceBookmarks = React.useCallback(
    async (items: ISplitViewListSingle[]) => {
      if (!openDB) return Promise.reject(new Error('no opendb'));

      setIsTransactionPending(true);
      const db = await openDB();

      try {
        const tx = db.transaction(BOOKMARKS_STORE, 'readwrite');
        const bookmarks = tx.objectStore(BOOKMARKS_STORE);

        // トランザクションの内容
        const operations = [
          // 全件削除
          bookmarks.clear(),
          // データ登録
          items.map((item) => bookmarks.put(item, item.id)),
          tx.done,
        ];

        return Promise.all(operations).then(() => Promise.resolve());

      } finally {
        db.close();
        await renewAllBookmarks();
        setIsTransactionPending(false);
      }
    }, [openDB, renewAllBookmarks],
  );

  /**
   * お気に入りを一件書き込む
   * 最大件数以上に登録しようとしたらrejectする
   * @return resolve時に書込み成功したitemのIDを返却する
   */
  const addBookmark: AddBookmark = React.useCallback(async (item: ISplitViewListSingle) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    if (allBookmarks.length >= MAX_BOOKMARKS_COUNT) {
      // 既にMAX_BOOKMARKS_COUNTの件数分登録があった場合はreject
      return Promise.reject(UseRepositoryError.MAX_BOOKMARKS);
    }

    setIsTransactionPending(true);
    const db = await openDB();
    const reposEntry = createNewBookmarkReposEntry(item);

    try {
      return db.put<typeof BOOKMARKS_STORE>(BOOKMARKS_STORE, reposEntry, reposEntry.id);
    } finally {
      db.close();
      setIsTransactionPending(false);
      renewAllBookmarks();
    }
  }, [openDB, allBookmarks, renewAllBookmarks]);

  /**
   * お気に入りを一件削除
   */
  const deleteBookmark: DeleteBookmark = React.useCallback(async (itemId: string) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    setIsTransactionPending(true);
    const db = await openDB();

    try {
      return db.delete<typeof BOOKMARKS_STORE>(BOOKMARKS_STORE, itemId);
    } finally {
      db.close();
      setIsTransactionPending(false);
      renewAllBookmarks();
    }
  }, [openDB, renewAllBookmarks]);

  /**
   * お気に入りキューとお気に入りを一件追加する
   * お気に入りキューとお気に入りの両方に影響がある
   */
  // TODO:これがお気に入りの記事IndexDBにキューを送っている
  // のでこれと同様の処理をteams設定機能で行う必要がある
  const addBookmarkQueue: AddBookmarkQueue = React.useCallback(
    async (data: ISplitViewListSingle, type: BookmarkQueueType) => {
      if (!openDB) return Promise.reject(new Error('no opendb'));

      // 連弾防止のため処理中は次の処理を開始させない
      if (isTransactionPending) return Promise.resolve();

      if (type === 'PUT' && allBookmarks.length >= MAX_BOOKMARKS_COUNT) {
        // 既にMAX_BOOKMARKS_COUNTの件数分登録があった場合はreject
        return Promise.reject(UseRepositoryError.MAX_BOOKMARKS);
      }

      // 連弾防止を開始
      setIsTransactionPending(true);

      const db = await openDB();

      try {
        const tx = db.transaction([BOOKMARKS_STORE, BOOKMARK_QUEUE_STORE], 'readwrite');
        const bookmarksStore = tx.objectStore(BOOKMARKS_STORE);
        const queueStore = tx.objectStore(BOOKMARK_QUEUE_STORE);

        const currentQueue = await queueStore.get(data.id);

        // 相反するキューが既に存在する場合
        const hasPairedQueueAlready = currentQueue && currentQueue.type !== type;

        await Promise.all([
          hasPairedQueueAlready
            // 相反するキューが存在する場合はそのキューを削除
            ? queueStore.delete(data.id)
            : queueStore.put({ type, data, date: new Date() }, data.id),
          // ローカルお気に入りを更新
          type === 'PUT' ? bookmarksStore.put(data, data.id) : bookmarksStore.delete(data.id),
          tx.done,
        ]);

        return Promise.resolve();

      } finally {
        db.close();
        await renewAllBookmarks();

        // 連弾防止を終了
        setIsTransactionPending(false);
      }
    }, [openDB, isTransactionPending, renewAllBookmarks, allBookmarks],
  );

  /**
   * お気に入りキューを一件削除
   * お気に入りストアには影響しない
   */
  const deleteBookmarkQueue: DeleteBookmarkQueue = React.useCallback(
    async (key: string) => {
      if (!openDB) return Promise.reject(new Error('no opendb'));

      const db = await openDB();

      try {
        return await db.delete(BOOKMARK_QUEUE_STORE, key);

      } finally {
        db.close();
      }
    }, [openDB],
  );

  /**
   * お気に入りキューを全件取得
   */
  const getBookmarkQueues: GetBookmarkQueues = React.useCallback(
    async (): Promise<IRepositoryBookmarkQueue[]> => {
      if (!openDB) return Promise.reject(new Error('no opendb'));

      const db = await openDB();
      try {
        return db
          .getAll<typeof BOOKMARK_QUEUE_STORE>(BOOKMARK_QUEUE_STORE);
      } finally {
        db.close();
      }
    }, [openDB],
  );

  /**
   * お気に入りキューを置き換える
   */
  const replaceBookmarkQueues: ReplaceBookmarkQueues = React.useCallback(
    async (queues: IRepositoryBookmarkQueue[]) => {
      if (!openDB) return Promise.reject(new Error('no opendb'));
      if (queues.length === 0) return Promise.resolve();

      setIsTransactionPending(true);
      const db = await openDB();

      try {
        const tx = db.transaction(BOOKMARK_QUEUE_STORE, 'readwrite');
        const bookmarks = tx.objectStore(BOOKMARK_QUEUE_STORE);

        // トランザクションの内容
        const operations = [
          // 全件削除
          bookmarks.clear(),
          // データ登録
          queues.map((item) => bookmarks.put(item, item.data.id)),
          tx.done,
        ];

        return Promise.all(operations).then(() => Promise.resolve());

      } finally {
        db.close();
        setIsTransactionPending(false);
      }
    }, [openDB],
  );

  /**
   * 差分があった場合に既存のお気に入り情報を更新する
   * @return resolve時に更新成功した記事(Item)のGUIDを返却する
   */
  const updateBookmark: UpdateBookmark = React.useCallback(async (toBe: ISplitViewListSingle) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    const original = allBookmarks.find((bookmark) => bookmark.id === toBe.id);
    // 更新したい記事IDがIndexedDBに存在しなかった場合
    if (!original) return undefined;

    // deep compare
    if (JSON.stringify(original) === JSON.stringify(toBe)) return undefined;
    // 更新元のupdatedDateが更新先よりも新しい場合
    if (dayjs(original.properties.updatedDate).isAfter(dayjs(toBe.properties.updatedDate))) {
      return undefined;
    }

    const reposEntry = createExistingBookmarkReposEntry(original, toBe);
    // property discarding to deep comparing
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { reposUpdatedDate: _, ...before } = original;
    // property discarding to deep comparing
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { reposUpdatedDate: __, ...after } = reposEntry;
    if (JSON.stringify(before) === JSON.stringify(after)) return undefined;

    const db = await openDB();

    setIsTransactionPending(true);
    try {
      return db.put<typeof BOOKMARKS_STORE>(BOOKMARKS_STORE, reposEntry, reposEntry.id);
    } finally {
      db.close();
      setIsTransactionPending(false);
      renewAllBookmarks();
    }
  }, [openDB, allBookmarks, renewAllBookmarks]);

  return {
    addBookmark: isInitialized ? addBookmark : undefined,
    updateBookmark: isInitialized ? updateBookmark : undefined,
    deleteBookmark: isInitialized ? deleteBookmark : undefined,
    replaceBookmarks: isInitialized ? replaceBookmarks : undefined,
    getBookmarkQueues: isInitialized ? getBookmarkQueues : undefined,
    addBookmarkQueue: isInitialized ? addBookmarkQueue : undefined,
    deleteBookmarkQueue: isInitialized ? deleteBookmarkQueue : undefined,
    replaceBookmarkQueues: isInitialized ? replaceBookmarkQueues : undefined,
    isTransactionPending,
    allBookmarks,
    bookmarkDict,
  };
};

export default UseBookmarkRepositoryAccessor;
