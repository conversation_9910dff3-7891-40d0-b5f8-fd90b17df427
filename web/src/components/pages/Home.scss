@import '../../styles/variables';
@import '../../styles/mixin';

.home {
  // バックグラウンドが常に全幅になるようにmin-widthをfit-contentに変更
  min-width: fit-content;
  background: var(--color-guide-background-2);
}

.home-layout {
  margin-left: auto;
  margin-right: auto;
  min-height: 100vh;
  display: flex;
  flex-flow: column;
}

// 画面遷移エフェクト秒数
$transition-duration: 0.25s;

// PC版左右余白
$pc-frame-margin: 28px;

// splitViewContainerのテンプレート内でクラスを指定している
.home-split-view-list {
  height: 100vh;
  width: $width-split-view-list;

  @include media-pc {
    padding-left: $pc-frame-margin;
    padding-right: 28px + 15px;
  }

  // 一定サイズ以下では%ベースのリキッドレイアウトになる
  @media screen and (max-width: $width-split-view-list + $width-split-view-detail) {
    width: 35%;
  }

  @include media-sp {
    width: 100%;
  }

  @include media-pc {
    padding-top: var(--length-margin-vertical);
    padding-bottom: var(--length-margin-vertical);
  }
}

// splitViewContainerのテンプレート内でクラスを指定している
.home-split-view-detail {
  position: absolute;
  top: 0;
  right: 0;
  width: calc(100% - #{$width-split-view-list});
  z-index: 1;

  @include media-pc() {
    top: var(--length-margin-vertical);
    bottom: var(--length-margin-vertical);
    right: $pc-frame-margin;
    width: calc(100% - #{$width-split-view-list});
  }

  // 一定サイズ以下では%ベースのリキッドレイアウトになる
  @media screen and (max-width: $width-split-view-list + $width-split-view-detail) {
    width: 65%;
  }

  @include media-sp {
    position: static;
    width: 100%;
    height: auto;
  }

  &::after {
    @include media-sp {
      content: '';
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      background-color: var(--color-custom-black);
      opacity: 0;

      transition: opacity ease-in $transition-duration;
    }
  }

  &.is-open {
    &::after {
      @include media-sp {
        opacity: 0.6;
      }
    }
  }

  >.split-view-detail {
    @include media-sp {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      transform: translate3d(0, 100%, 0);
      transition-delay: 0s;
      transition: transform ease-out $transition-duration;
      z-index: 2;
    }

    &.is-open {
      @include media-sp {
        @include fix-ios-redraw-issue;

        transform: translateY(0);
      }

      @for $i from 1 to 99 {
        &.is-shifted-#{$i} {
          @include media-sp {
            transition: transform linear 0s;
            transform: translate3d(0, 0% + $i, 0);
          }
        }
      }
    }
  }
}

// お気に入り追加／削除時に表示されるポップアップメッセージ
// 一覧側
.home-bookmark-popup-1 {
  z-index: 2;
  left: $width-split-view-list * 0.5;

  // 一定サイズ以下では%ベースのリキッドレイアウトになる
  @media screen and (max-width: $width-split-view-list + $width-split-view-detail) {
    left: 17.5%;
    width: 35%;
  }

  @include media-sp {
    width: 100%;
    left: 50%;
  }
}

// 詳細側
.home-bookmark-popup-2 {
  z-index: 2;
  left: calc((100% + #{$width-split-view-list}) / 2);

  // 一定サイズ以下では%ベースのリキッドレイアウトになる
  @media screen and (max-width: $width-split-view-list + $width-split-view-detail) {
    left: 35% + (65 * 0.5);

    @include media-sp {
      width: 100%;
      left: 50%;
    }
  }
}

// アプリInfoを開いたときの黒背景
.home-app-info {
  >.home-app-info-shield {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;

    background: var(--color-custom-black);
    pointer-events: none;
    user-select: none;

    // transition
    opacity: 0;
    transition: opacity $transition-duration ease-in;
  }

  &.is-open {

    // 黒背景へのクリックが透過しないようにする
    >.home-app-info-shield {
      pointer-events: auto;
      opacity: 0.65;
    }
  }
}

.home-app-info-menu {
  position: absolute;
  top: var(--length-margin-vertical);
  bottom: var(--length-margin-vertical);
  z-index: 3;

  // PCでの開閉スタイル
  @include media-pc {
    // 左右に余白18pxを設ける
    width: calc(100vw - 36px);
    // モーダル自体が最大で1122pxまで広がる
    max-width: 1122px;
    max-height: 80%;
    // それ以上大きいときは画面とモーダルの間の余白が広がる
    margin: auto;
    left: 0;
    right: 0;

    // 開閉後に変化するプロパティ
    opacity: 0;
    transition: opacity $transition-duration ease-out;
    pointer-events: none;

    &.is-open {
      pointer-events: auto;
      opacity: 1;
    }
  }

  // SPでの開閉スタイル
  @include media-sp {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transform: translate3d(0, 100%, 0);
    transition-delay: 0s;
    transition: transform ease-out $transition-duration;
    z-index: 2;

    &.is-open {
      @include fix-ios-redraw-issue;

      transform: translateY(8px);

      @for $i from 1 to 99 {
        &.is-shifted-#{$i} {
          transition: transform linear 0s;
          transform: translate3d(0, 0% + $i, 0);
        }
      }
    }
  }
}

// TeamsSettingModal用のスタイル
.home-simple-modal {
  >.home-simple-modal-shield {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 4;
    background: var(--color-custom-black);
    pointer-events: none;
    user-select: none;

    // transition
    opacity: 0;
    transition: opacity $transition-duration ease-in;
  }

  &.is-open {

    // 黒背景へのクリックが透過しないようにする
    >.home-simple-modal-shield {
      pointer-events: auto;
      opacity: 0.65;
    }
  }
}

.home-simple-modal-content {
  position: absolute;
  top: var(--length-margin-vertical);
  bottom: var(--length-margin-vertical);
  z-index: 5;

  // PCでの開閉スタイル
  @include media-pc {
    // 左右に余白18pxを設ける
    width: calc(100vw - 36px);
    // モーダル自体が最大で600pxまで広がる
    max-width: 600px;
    max-height: 80%;
    // それ以上大きいときは画面とモーダルの間の余白が広がる
    margin: auto;
    left: 0;
    right: 0;

    // 開閉後に変化するプロパティ
    opacity: 0;
    transition: opacity $transition-duration ease-out;
    pointer-events: none;

    &.is-open {
      pointer-events: auto;
      opacity: 1;
    }
  }

  // SPでの開閉スタイル
  @include media-sp {
    left: 0;
    right: 0;

    // 開閉後に変化するプロパティ
    transform: translateX(100%);
    transition: transform $transition-duration ease-out;

    &.is-open {
      transform: translateX(0);
    }
  }
}