import * as React from 'react';
import {
  <PERSON><PERSON>, <PERSON><PERSON>, Input, Loader,
} from '@fluentui/react-northstar';
import { AddIcon, AcceptIcon, CloseIcon } from '@fluentui/react-icons-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import useUserChatsAndChannelsAccessor, { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';
import TeamsSettingTabs, { TeamsSettingTabType, TeamsSettingTabTypeValue } from './TeamsSettingTabs';
import SelectedItemsList from './SelectedItemsList';

// CSS
import './TeamsSettingModal.scss';
import { UseTeamsChatsApiReturnType, ITeamsChatsRequest } from '../../../../hooks/accessors/useTeamsChatsApiAccessor';
import MessageToaster, { ToasterMessage } from '../../../commons/molecules/message-toaster/MessageToaster';
import useMessageToasterBehavior from '../../../../hooks/behaviors/useMessageToasterBehavior';

// チャットアイテムの型定義
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
}

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  onClose: () => void;
  useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType,
  // userId?: string; // ユーザーIDの登録も必要かも
}

/**
 * TeamsSettingModal
 * @param props
 */
const TeamsSettingModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    onClose,
    useTeamsChatsApiAccessorReturn,
  } = props;

  const { postTeamsChatsApi } = useTeamsChatsApiAccessorReturn;

  /**
   * ラベル
   */
  const TeamsSettingLabel = {
    TITLE: 'Teams設定',
  };

  // コンポーネント初期化ユーティリティ
  const [, , callbacks] = useComponentInitUtility({
    componentName: 'TeamsSettingModal',
  });
  const tokenProvider = React.useMemo(() => {
    if (!callbacks?.get) return undefined;
    const graphTokenProvider = callbacks.get('graph');
    return graphTokenProvider ? () => graphTokenProvider() : undefined;
  }, [callbacks]);

  // APIアクセサーを初期化
  const {
    fetchUserChatsAndChannels,
    isLoading, error,
  } = useUserChatsAndChannelsAccessor(tokenProvider);

  // 状態管理
  const [searchQuery, setSearchQuery] = React.useState('');
  const [allChatItems, setAllChatItems] = React.useState<IUserChatItem[]>([]);
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = React.useState<TeamsSettingTabTypeValue>(
    TeamsSettingTabType.CHAT,
  );
  const [isSaving, setIsSaving] = React.useState(false);

  // トースター機能
  const [isToasterShown, toasterMessage, extendPopupTimer] = useMessageToasterBehavior(3000);

  // 選択上限数
  const MAX_SELECTION_COUNT = 10;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    const hasSelectedItems = selectedItems.size > 0 ? 'has-selected-items' : '';
    return mergedClassName(mergedClassName(isOpen, step1), hasSelectedItems);
  }, [className, open, selectedItems.size]);

  // データ取得のEffect
  React.useEffect(() => {
    if (open && fetchUserChatsAndChannels) {
      fetchUserChatsAndChannels()
        .then((items) => {
          setAllChatItems(items);
          setFilteredChatItems(items);
        })
        .catch((err) => {
          // エラーを再スローして上位でハンドリングできるようにする
          throw err;
        });
    }
  }, [open, fetchUserChatsAndChannels]);

  // 検索フィルタリングのEffect（タブとテキスト検索の両方に対応）
  React.useEffect(() => {
    let filtered = allChatItems;

    // タブによるフィルタリング
    filtered = filtered.filter((item) => item.type === activeTab);

    // テキスト検索によるフィルタリング
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) => item.id.toLowerCase().includes(searchQuery.toLowerCase())
        || item.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    setFilteredChatItems(filtered);
  }, [searchQuery, allChatItems, activeTab]);

  const handleClose = React.useCallback(() => {
    if (onClose) onClose();
  }, [onClose]);

  // 検索クエリ入力の変更ハンドラー
  const handleSearchQueryChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setSearchQuery(data?.value ?? '');
    },
    [],
  );

  // タブ変更ハンドラー
  const handleTabChange = React.useCallback((tab: TeamsSettingTabTypeValue) => {
    setActiveTab(tab);
    // タブ切り替え時に検索クエリをクリア
    setSearchQuery('');
  }, []);

  // チャットとチャネルの件数を計算
  const chatCount = React.useMemo(() => allChatItems.filter((item) => item.type
  === TeamsSettingTabType.CHAT).length, [allChatItems]);

  const channelCount = React.useMemo(() => allChatItems.filter((item) => item.type
  === TeamsSettingTabType.CHANNEL).length, [allChatItems]);

  // プレースホルダーテキストをタブに応じて変更
  const searchPlaceholder = React.useMemo(() => (activeTab === TeamsSettingTabType.CHAT ? 'チャット名で検索' : 'チャネル名で検索'), [activeTab]);

  // アイテム選択切り替えハンドラー
  const handleItemToggle = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        // 選択解除の場合
        newSet.delete(id);
      } else {
        // 選択追加の場合：上限チェック
        if (newSet.size >= MAX_SELECTION_COUNT) {
          // 上限に達している場合はトースターメッセージを表示
          extendPopupTimer(ToasterMessage.MAX_TEAMS_SELECTION);
          return prev; // 状態を変更しない
        }
        newSet.add(id);
      }
      return newSet;
    });
  }, [extendPopupTimer]);

  // 選択されたアイテムを削除するハンドラー
  const handleRemoveSelectedItem = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  // キーボードイベントハンドラー
  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, id: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemToggle(id);
    }
  }, [handleItemToggle]);

  // 保存ハンドラー
  const handleSave = React.useCallback(async () => {
    console.log('=== handleSave開始 ===');
    console.log('postTeamsChatsApi:', postTeamsChatsApi);
    console.log('selectedItems.size:', selectedItems.size);

    if (!postTeamsChatsApi || selectedItems.size === 0) {
      console.log('早期リターン: postTeamsChatsApi または selectedItems が無効');
      return;
    }

    // ローディング状態を開始
    setIsSaving(true);

    try {
      // 選択されたアイテムを取得してAPIリクエストを作成
      const selectedChatItems = allChatItems.filter((item) => selectedItems.has(item.id));
      console.log('選択されたアイテム:', selectedChatItems);

      // 各選択されたアイテムに対してAPIを呼び出し
      await Promise.all(selectedChatItems.map(async (item, index) => {
        console.log(`=== アイテム ${index + 1} の処理開始 ===`);
        console.log('item:', item);
        console.log('item.type:', item.type);
        console.log('item.chatType:', item.chatType);
        console.log('item.teamId:', item.teamId);
        console.log('item.id:', item.id);

        const request: ITeamsChatsRequest = {
        // countIdは選択順序を使用 (1から開始)
          countId: index + 1,
          chatType: item.chatType,
          // チャットの場合
          ...(item.type === 'チャット' && {
            chatId: item.id,
          }),
          // チャネルの場合
          ...(item.type === 'チャネル' && {
            teamId: item.teamId,
            channelId: item.id,
          }),
        };

        console.log('APIリクエスト:', request);
        console.log('postTeamsChatsApi呼び出し前');

        try {
          await postTeamsChatsApi(request);
          console.log(`アイテム ${index + 1} のAPI呼び出し成功`);
        } catch (apiError) {
          console.error(`アイテム ${index + 1} のAPI呼び出しエラー:`, apiError);
          throw apiError;
        }
      }));

      console.log('全てのAPI呼び出し完了');
      // 保存成功後にモーダルを閉じる
      handleClose();
    } catch (e) {
      console.error('保存処理でエラーが発生しました:', e);
      // エラーが発生した場合もローディング状態を解除
    } finally {
      // ローディング状態を終了
      setIsSaving(false);
    }
  }, [postTeamsChatsApi, selectedItems, allChatItems, handleClose, setIsSaving]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={TeamsSettingLabel.TITLE} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <p>検索対象を選択できます。</p>

            {/* タブ切り替え */}
            <TeamsSettingTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              disabled={isLoading}
              chatCount={chatCount}
              channelCount={channelCount}
            />

            {/* 検索フィールド */}
            <div className="simple-modal-chat-input">
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={handleSearchQueryChange}
                fluid
              />
            </div>

            {/* 選択されたアイテム一覧 */}
            <SelectedItemsList
              selectedItems={selectedItems}
              allChatItems={allChatItems}
              onRemoveItem={handleRemoveSelectedItem}
            />

            {/* チャットアイテム一覧 */}
            <div className="simple-modal-chat-items">
              {isLoading && (
                <div className="simple-modal-loading">
                  <p>チャットとチャネルを読み込み中...</p>
                </div>
              )}
              {error && (
                <div className="simple-modal-error">
                  <p>
                    エラーが発生しました:
                    {error}
                  </p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.length === 0 && (
                <div className="simple-modal-no-results">
                  <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.map((item) => {
                const isSelected = selectedItems.has(item.id);
                const itemClassName = `simple-modal-chat-item${isSelected ? ' selected' : ''}`;
                return (
                  <div
                    key={item.id}
                    className={itemClassName}
                    onClick={() => handleItemToggle(item.id)}
                    onKeyDown={(event) => handleKeyDown(event, item.id)}
                    role="button"
                    tabIndex={0}
                    aria-pressed={isSelected}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="simple-modal-chat-item-content">
                      <span className="simple-modal-chat-item-name">{item.name}</span>
                    </div>
                    {isSelected ? (
                      <AcceptIcon
                        style={{
                          color: 'var(--color-guide-brand-icon)',
                          fontSize: '20px',
                          transform: 'scale(1.1)',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    ) : (
                      <AddIcon
                        style={{
                          color: 'var(--color-guide-foreground-2)',
                          fontSize: '20px',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>

            {/* 保存ボタン */}
            <div className="simple-modal-save-section" style={{ marginTop: '20px', padding: '0 20px' }}>
              <Button
                primary
                content={
                  isSaving ? (
                    <>
                      <Loader size="smallest" inline />
                      {' '}
                      保存中...
                    </>
                  ) : (
                    '保存'
                  )
                }
                disabled={selectedItems.size === 0 || !postTeamsChatsApi || isSaving}
                onClick={handleSave}
                fluid
              />
            </div>
          </div>
        </div>
      </div>

      {/* トースターメッセージ */}
      <MessageToaster
        isActive={isToasterShown}
        messageType={toasterMessage}
      />
    </div>
  );
};

TeamsSettingModal.defaultProps = {
  className: '',
  open: false,
  // userId: undefined,
};

export default TeamsSettingModal;
