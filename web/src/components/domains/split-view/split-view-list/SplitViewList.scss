@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.split-view-list {
  display: flex;
  flex-flow: column nowrap;
  width: 100%;
  height: 100%;

  &.is-default {
    .split-view-list-scroll {
      @include media-sp {
        background-color: var(--color-guide-background-2);
      }
    }
  }
}

.split-view-list-title {
  padding-left: 0;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;

  .split-view-title-company-row {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }

  .split-view-title-main-row {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }

  @include media-pc {
    margin-top: 10px;
    padding-bottom: 18px - 4px;
  }

  @include media-sp {
    padding-top: 10px;
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
    background-color: var(--color-guide-background);
    height: 47px + 40px;
    max-width: 100vw;
  }
}

// a part showing companyName
.split-view-title-company {
  margin: 0;
  flex-grow: 0;

  @include media-pc {
    font-size: 13px;
    margin-bottom: 5px;
  }

  @include media-sp {
    margin-top: 15px;
    font-size: 12px;
  }
}

// a part showing spoListTitle
.split-view-title-main {
  margin: 0;
  flex-grow: 0;

  @include media-pc {
    font-size: 23px;
    margin-left: 11px;
    margin-top: -8px;
  }

  @include media-sp {
    margin-top: -8px;
  }
}


// appInfo message
.split-view-list-app-info {
  padding: 0px 0px 0px 0px;
}

.split-view-list-companyRegulationSite-info {
  @include media-sp {
    display: none;
  }
}

.split-view-list-scroll {

  // ローディング中はスクロールを抑止
  &.is-loading {
    pointer-events: none;
  }

  &.is-error {
    @include media-sp {
      @include fix-ios-redraw-issue;
    }
  }
}

.split-view-list-card,
.split-view-list-content {
  display: flex;
  flex-flow: column;
  height: calc(100% + 25px);
}

.split-view-list-card {
  @include media-pc {
    border-radius: 4px;
    background-color: var(--color-guide-background);
    box-shadow: $box-shadow-1;
    overflow: hidden;
  }
}

.split-view-list-header-container {
  @include media-sp {
    // box-shadow: $box-shadow-4;
    position: relative;
    z-index: 1;
  }
}

.split-view-list-header-tab {
  // border-bottom: 1px solid var(--color-guide-foreground-6);
  // display: flex;
  // flex-flow: row nowrap;
  // justify-content: flex-start;
  // align-items: flex-end;
  display: flex;
  height: 24px;

}

.settings-btn {
  //TODO:改善必要かも
  margin-left: auto;
  margin-right: -5px;

  >* {
    width: 17px;
    height: 17px;
    fill: var(--color-guide-brand-main-foreground);
  }
}

.split-view-list-header {
  padding-top: var(--length-margin-vertical);
  padding-bottom: var(--length-margin-vertical);
  padding-left: var(--length-margin-horizontal);
  padding-right: var(--length-margin-horizontal);

  @include media-pc {
    position: relative;
    z-index: 1;
  }

  @include media-sp {
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
    padding-top: 13px;
    padding-bottom: 13px;
    background-color: var(--color-guide-background-2);
  }
}

.split-view-list-header-bookmarks-head,
.split-view-list-header-search-result-head {
  border-top: 1px solid var(--color-guide-foreground-6);
  border-bottom: 1px solid var(--color-guide-foreground-6);
  margin-top: -10px;
}

.split-view-list-header-row {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
  margin-top: 5px;

  &.filter-and-sorter {
    flex-wrap: wrap;
    column-gap: 5px;
    row-gap: 8px;
  }

  >* {
    margin: 0;
  }

  .split-view-list-search-bar {
    margin-right: 20px;

    // ハイライト実装により画面描画に失敗するバグの暫定対応
    @include media-sp {
      @include fix-ios-redraw-issue;
    }
  }

  .split-view-list-header-switch {
    margin-left: auto;
    margin-right: -5px;
    // margin-right: -22px;
    // margin-bottom: 55px;
  }
}

.too-many-search-results-message {
  font-size: 0.75rem;
  margin-top: 5px;
  margin-left: 4px;
}

.split-view-list-sort-button {
  margin-left: -10px;
  margin-top: -4px;
}

.split-view-list-header-loader {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 5px;
}

.split-view-list-message-container {
  display: flex;
  flex-grow: 1;
  height: 100%;
}

.split-view-list-app-info-messages {
  padding-left: 1.3rem;
  padding-right: 1.3rem;
}

.split-view-list-message {
  display: flex;
  flex-grow: 1;
  height: 100%;
  padding-left: 20px;
  padding-right: 20px;

  position: relative;
  top: -25px;
  padding-bottom: 20px;
}

.split-view-list-message-no-bookmarks {
  position: relative;
  // list-headerの高さ分シフト
  top: -36px;
}

.split-view-list-message-no-searchresult {
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  word-break: break-all;
  // list-headerの高さ分シフト
  padding-top: 36px;

  .information-message {
    display: inline-flex;
    flex-wrap: nowrap;
    align-items: center;
    padding-top: 1rem;

    button {
      margin-left: -0.1rem;
    }
  }
}

.split-view-list-message-no-searchresult-desc {
  font-size: smaller;
  flex-direction: column;
  display: flex;
}

.split-view-list-message-no-unread {
  position: relative;
  flex-direction: column;
  justify-content: flex-start;
  // list-headerの高さ分シフト
  padding-top: 50px;
}

.split-view-list-message-too-many-retry {
  position: relative;
  flex-direction: column;
  justify-content: flex-start;
  // list-headerの高さ分シフト
  padding-top: 36px;
}

.split-view-list-list {
  flex-grow: 1;
  display: flex;
  flex-flow: column nowrap;
  background-color: var(--color-guide-background);
  margin-top: 0;
  list-style: none;

  // 下へ多めに余白をとっておく
  padding: 0 0 4em;

  @include media-sp {
    background-color: var(--color-guide-background-2);
  }
}

.split-view-list-item {
  border-bottom: 1px solid var(--color-guide-foreground-6);
}

.ui-text+.here {
  min-width: 0;
  margin: 0;
  padding: 0;
  display: inline;
  color: var(--color-guide-brand-main-foreground);

  &:hover {
    color: var(--color-guide-brand-main-foreground);
  }
}