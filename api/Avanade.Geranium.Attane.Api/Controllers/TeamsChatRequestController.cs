using Azure.Data.Tables;
using Azure;

namespace Avanade.Geranium.Attane.Api.Controllers
{
    /// <summary>
    /// Provides the Teams Chat Request Web API functionality.
    /// </summary>
    [Authorize]
    [Route("users")]
    [Produces(System.Net.Mime.MediaTypeNames.Application.Json)]
    public class TeamsChatRequestController : ControllerBase
    {
        private readonly ILogger<TeamsChatRequestController> _logger;
        private readonly IConfiguration _configuration;
        private const string TableName = "TeamsChats";

        /// <summary>
        /// Initializes a new instance of the <see cref="TeamsChatRequestController"/> class.
        /// </summary>
        /// <param name="logger">The <see cref="ILogger{TeamsChatRequestController}"/>.</param>
        /// <param name="configuration">The <see cref="IConfiguration"/>.</param>
        public TeamsChatRequestController(
            ILogger<TeamsChatRequestController> logger,
            IConfiguration configuration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Teams チャット設定を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The Teams chat request.</param>
        /// <returns>The created Teams chat configuration.</returns>
        [HttpPost("{userId}/teams-chats")]
        [ProducesResponseType((int)HttpStatusCode.Created)]
        public async Task<IActionResult> RegisterTeamsChat(string userId, [FromBody] TeamsChatsRequest request)
        {
            var startTime = DateTime.UtcNow;
            Console.WriteLine($"[DEBUG] Teams chat registration started for user: {userId}");
            Console.WriteLine($"[DEBUG] Request data: CountId={request?.countId}, ChatType={request?.chatType}, chatId={request?.chatId}, ChannelId={request?.channelId}, TeamId={request?.teamId}");
            _logger.LogInformation("Teams chat registration started for user {UserId}", userId);
            try
            {
                // リクエストの検証
                if (request == null)
                {
                    _logger.LogError("Request is null for user {UserId}", userId);
                    return BadRequest(new { error = "Request cannot be null." });
                }
                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogError("UserId is empty");
                    return BadRequest(new { error = "UserId cannot be empty." });
                }

                // Table Storageエンティティを作成
                var id = Guid.NewGuid().ToString();
                var rowKey = request.chatType == "TeamsChannel" ? request.channelId ?? string.Empty : request.chatId ?? string.Empty;
                Console.WriteLine($"[DEBUG] Creating table entity - PartitionKey: {userId}, RowKey: {rowKey}");

                var tableEntity = new SimpleTeamsChatsEntity
                {
                    PartitionKey = userId,
                    RowKey = rowKey,
                    countId = request.countId,
                    chatId = request.chatId,
                    channelId = request.channelId,
                    teamId = request.teamId,
                    chatType = request.chatType,
                    // CreatedAt = now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    // UpdatedAt = now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                };

                Console.WriteLine($"[DEBUG] Getting table client...");
                var tableClient = await GetTableClientAsync();
                Console.WriteLine($"[DEBUG] Table client obtained, adding entity...");
                await tableClient.AddEntityAsync(tableEntity);
                Console.WriteLine($"[DEBUG] Entity added successfully");

                // レスポンスを作成
                var response = new TeamsChatsResponse
                {
                    Id = id,
                    countId = request.countId,
                    chatId = request.chatId,
                    channelId = request.channelId,
                    teamId = request.teamId,
                    chatType = request.chatType ?? string.Empty,
                    UserId = userId,
                    // TODO:一旦不要
                    // CreatedAt = now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") ?? string.Empty,
                    // UpdatedAt = now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") ?? string.Empty
                };

                var duration = DateTime.UtcNow - startTime;
                _logger.LogInformation("Teams chat registration completed in {Duration}ms for user {UserId}", duration.TotalMilliseconds, userId);

                return StatusCode((int)HttpStatusCode.Created, response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ERROR: Exception occurred - Type: {ex.GetType().Name}, Message: {ex.Message}");
                Console.WriteLine($"[DEBUG] ERROR: Stack trace: {ex.StackTrace}");
                _logger.LogError(ex, "Unexpected error in Teams chat registration for user {UserId}", userId);
                return StatusCode(500, new { error = $"Unexpected error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Teams チャット設定を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="chatId">The チャットID または チャネルID.</param>
        /// <returns>No content if successful.</returns>
        [HttpDelete("{userId}/teams-chats/{chatId}")]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        [ProducesResponseType((int)HttpStatusCode.NotFound)]
        public async Task<IActionResult> DeleteTeamsChat(string userId, string chatId)
        {
            var startTime = DateTime.UtcNow;
            Console.WriteLine($"[DEBUG] Teams chat deletion started for user: {userId}, chatId: {chatId}");
            _logger.LogInformation("Teams chat deletion started for user {UserId}, chatId {ChatId}", userId, chatId);

            try
            {
                // パラメータの検証
                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogError("UserId is empty");
                    return BadRequest(new { error = "UserId cannot be empty." });
                }

                if (string.IsNullOrEmpty(chatId))
                {
                    _logger.LogError("ChatId is empty");
                    return BadRequest(new { error = "ChatId cannot be empty." });
                }

                // Table Storageから削除
                Console.WriteLine($"[DEBUG] Deleting table entity - PartitionKey: {userId}, RowKey: {chatId}");

                var tableClient = await GetTableClientAsync();

                try
                {
                    await tableClient.DeleteEntityAsync(userId, chatId);
                    Console.WriteLine($"[DEBUG] Entity deleted successfully");
                }
                catch (Azure.RequestFailedException ex) when (ex.Status == 404)
                {
                    Console.WriteLine($"[DEBUG] Entity not found - PartitionKey: {userId}, RowKey: {chatId}");
                    _logger.LogWarning("Teams chat not found for user {UserId}, chatId {ChatId}", userId, chatId);
                    return NotFound(new { error = "Teams chat configuration not found." });
                }

                var duration = DateTime.UtcNow - startTime;
                _logger.LogInformation("Teams chat deletion completed in {Duration}ms for user {UserId}, chatId {ChatId}",
                    duration.TotalMilliseconds, userId, chatId);

                return NoContent();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ERROR: Exception occurred - Type: {ex.GetType().Name}, Message: {ex.Message}");
                Console.WriteLine($"[DEBUG] ERROR: Stack trace: {ex.StackTrace}");
                _logger.LogError(ex, "Unexpected error in Teams chat deletion for user {UserId}, chatId {ChatId}", userId, chatId);
                return StatusCode(500, new { error = $"Unexpected error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Table Clientを取得します.
        /// </summary>
        /// <returns>The Table Client.</returns>
        private async Task<TableClient> GetTableClientAsync()
        {
            Console.WriteLine($"[DEBUG] Getting connection string from configuration...");
            var connectionString = _configuration["StorageAccount:ConnectionString2"];
            Console.WriteLine($"[DEBUG] Connection string obtained: {(!string.IsNullOrEmpty(connectionString) ? "YES" : "NO")}");

            if (string.IsNullOrEmpty(connectionString))
            {
                Console.WriteLine($"[DEBUG] ERROR: Storage connection string is not configured.");
                throw new InvalidOperationException("Storage connection string is not configured.");
            }

            Console.WriteLine($"[DEBUG] Creating TableClient for table: {TableName}");
            var client = new TableClient(connectionString, TableName);
            Console.WriteLine($"[DEBUG] Creating table if not exists...");
            await client.CreateIfNotExistsAsync();
            Console.WriteLine($"[DEBUG] Table client ready");
            return client;
        }
    }

    /// <summary>
    /// Teams チャット設定のリクエスト.
    /// </summary>
    public class TeamsChatsRequest
    {
        /// <summary>
        /// Gets or sets the カウントID.
        /// </summary>
        public int countId { get; set; }

        /// <summary>
        /// Gets or sets the チャットID.
        /// </summary>
        public string? chatId { get; set; }

        /// <summary>
        /// Gets or sets the チャネルID.
        /// </summary>
        public string? channelId { get; set; }

        /// <summary>
        /// Gets or sets the チームID.
        /// </summary>
        public string? teamId { get; set; }

        /// <summary>
        /// Gets or sets the チャットタイプ.
        /// </summary>
        public string? chatType { get; set; }
    }

    /// <summary>
    /// Teams チャット設定のレスポンス.
    /// </summary>
    public class TeamsChatsResponse
    {
        /// <summary>
        /// Gets or sets the ID.
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the チャットID.
        /// </summary>
        public string? chatId { get; set; }

        /// <summary>
        /// Gets or sets the チャネルID.
        /// </summary>
        public string? channelId { get; set; }

        /// <summary>
        /// Gets or sets the チャットタイプ.
        /// </summary>
        public string chatType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the カウントID.
        /// </summary>
        public int countId { get; set; }

        /// <summary>
        /// Gets or sets the チームID.
        /// </summary>
        public string? teamId { get; set; }

        /// <summary>
        /// Gets or sets the ユーザーID.
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the 作成日時.
        /// </summary>
        public string CreatedAt { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the 更新日時.
        /// </summary>
        public string UpdatedAt { get; set; } = string.Empty;
    }

    /// <summary>
    /// Simple Teams チャット設定のTable Storage エンティティ.
    /// </summary>
    public class SimpleTeamsChatsEntity : ITableEntity
    {
        /// <summary>
        /// パーティションキー.
        /// </summary>
        public string PartitionKey { get; set; } = string.Empty;

        /// <summary>
        /// 行キー.
        /// </summary>
        public string RowKey { get; set; } = string.Empty;

        /// <summary>
        /// タイムスタンプ.
        /// </summary>
        public DateTimeOffset? Timestamp { get; set; }

        /// <summary>
        /// ETag.
        /// </summary>
        public ETag ETag { get; set; }

        /// <summary>
        /// チャットID.
        /// </summary>
        public string? chatId { get; set; }

        /// <summary>
        /// チャンネルID.
        /// </summary>
        public string? channelId { get; set; }

        /// <summary>
        /// チャットタイプ.
        /// </summary>
        public string? chatType { get; set; }

        /// <summary>
        /// カウントID.
        /// </summary>
        public int countId { get; set; }

        /// <summary>
        /// チームID.
        /// </summary>
        public string? teamId { get; set; }

        /// <summary>
        /// 作成日時.
        /// </summary>
        public string? CreatedAt { get; set; }

        /// <summary>
        /// 更新日時.
        /// </summary>
        public string? UpdatedAt { get; set; }
    }
}