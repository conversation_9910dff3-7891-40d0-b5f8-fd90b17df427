import { BatchRequestData, Client } from "@microsoft/microsoft-graph-client";
import Dictionary from "../types/Dictionary";
import HttpStatusCode from "../types/HttpStatusCode";
import IBatchResult from "../types/IBatchResult";
import ISearchContext from "../types/ISearchContext";
import ISearchParameters from "../types/ISearchParameters"
import { TokenProvider } from "../types/TokenProvider";
import { executeAll } from "../utilities/api";
import { toUTC } from "../utilities/date";
import { MAX_PAGE_SPO, MAX_RESULT_SPO } from "../utilities/lib";

import { createCamlBodyWithSearchWords, createListUrlOfCaml } from "../utilities/sharepoint";
import { createSpClient } from "../utilities/spClient";
import { parseSharePointPath } from "../utilities/url";
// トークンに応じた検索結果をリクエスト
const searchFromSpo = async (parameters: ISearchParameters): Promise<IBatchResult[]> => {

  const token = parameters.res['Spo'];
  const tokenProvider: TokenProvider = () => Promise.resolve(token);

  const getClient = (param: Dictionary<string, string>): Client => {
    const requestBaseUrl = param['site'];
    const [customHost] = parseSharePointPath(requestBaseUrl);
    if (!customHost) {
      throw new Error('Cannot create client');
    }
    const client = createSpClient(tokenProvider, requestBaseUrl, customHost);
    if (!client) {
      throw new Error('Cannot create client');
    }
    return client;
  }
  // ここで取得したListからGUIDを取得しているだけでIDは生成してない
  const getRequest: (context: ISearchContext) => BatchRequestData = (context) => {
    const body = JSON.stringify(createCamlBodyWithSearchWords(
      MAX_RESULT_SPO + 1,
      ['Title', 'docBody', context.properties['category']],
      parameters.conditionKeywords,
      !!context.properties['filterByPresentPeriod'] && context.properties['filterByPresentPeriod'].toLowerCase() !== 'false',
      context.paging?.id,
      context.paging?.modified),
    );
    parameters.logger?.verbose(`body: ${body}`);
    return ({
      id: '',
      url: `${context.properties['site']}_api/${createListUrlOfCaml(context.properties['list'])}`,
      method: 'POST',
      headers: {
        accept: 'application/json;odata=verbose',
      },
      body,
    });
  };

  /*
  const getRequestGet: (param: Dictionary<string, string>) => BatchRequestData = (param) => {
    const caml = JSON.stringify(createCamlBodyWithSearchWords(
      MAX_RESULT + 1,
      ['Title', 'docBody', param['category']],
      parameters.conditionKeywords,
      !!param['filterByPresentPeriod']).query);
    return {
      id: '',
      url: `${param['site']}_api/${createListUrlOfCaml(param['list'])}(query=@v1)?@v1=${encodeURIComponent(caml)}`,
      method: 'GET',
      headers: {
        'content-type': 'application/json',
        accept: 'application/json;odata=verbose',
      },
    } as BatchRequestData;
  };
  */

  const getIds = (r: any) => (r.value as any[]).filter(v => v.GUID).map(v => v.GUID as string) ?? [];
  const getPagingContext = (context: ISearchContext, r: any[]) => {
    const filtered = r.filter(v => v.GUID);

    if (filtered.length <= MAX_RESULT_SPO) {
      return undefined;
    }
    const pageLast = filtered[MAX_RESULT_SPO - 1];
    return {
      id: pageLast.ID,
      modified: toUTC(`${pageLast.Modified}${context.properties.timezoneOffset ?? ''}`),
    };
  }

  return (await executeAll({
    process: 'graph',
    //client: createGlobalClient(parameters, tokenProvider),
    //endPoint: `${parameters.properties.global?.['site']?.replace(/\/$/, '')}/_api`,
    //token,
    context: parameters.properties.local,
    getClient,
    getRequest: getRequest,
    getIds,
    getPagingContext,
    getUniqueKey: (context: ISearchContext) => `${context.properties['list']}-${context.page}`,
  })).map(result => {
    if (!result.success && result?.error?.statusCode === HttpStatusCode.Forbidden) {
      // 権限がなくて取得できなかった場合は、単に見つからなかったことにする
      parameters.logger?.verbose(`Forbidden: ${result?.error?.message}`);
      return {
        ...result,

        success: true,
        error: undefined,
        hasNext: false,
        ids: [],
      };
    }

    const exceedsLength = (result.ids?.length ?? 0) > MAX_RESULT_SPO;
    const hasNext = exceedsLength && result.page < MAX_PAGE_SPO
    return {
      ...result,
      hasNext,
      ids: exceedsLength ? result.ids?.slice(0, MAX_RESULT_SPO) : result.ids,
    };
  });
}

function removeUrlProtocol(url: string): string {
  return url.replace(/^.*\/\//, '');
}

function getHost(url: string): string {
  return url.replace(/^.*\/\//, '').replace(/[\/?].*/, '');
}

function encode(str: string): string {
  return str
    .replace(/%/g, '%25')
    .replace(/&/g, '%26')
    .replace(/\+/g, '%2B')
    .replace(/ /g, '+');
}

function createGlobalClient(parameters: ISearchParameters, tokenProvider: TokenProvider): Client {
  const baseUrl = parameters.properties.global['site'];
  const client = createSpClient(tokenProvider, baseUrl, getHost(baseUrl));

  if (!client) {
    throw new Error('Cannot create client');
  }
  return client;
}

export default searchFromSpo;

