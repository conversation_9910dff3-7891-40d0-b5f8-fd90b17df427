import * as dotenv from 'dotenv';

dotenv.config();

interface ISPOListItemRequest {
  id: string;
  method: string;
  url: string;
 headers?: { [key: string]: string };
}

const SPO_LIST_ITEMS_SEARCH_SIZE: string = process.env['SPO_LIST_ITEMS_SEARCH_SIZE'] ?? '50';
const SPO_LIST_ITEMS_MAX_YEARS: string = process.env['SPO_LIST_ITEMS_MAX_YEARS'] ?? '1';

function getRollingYearDateRange(): { start: string; end: string } {
  const today = new Date();
  
  const maxYears = parseInt(SPO_LIST_ITEMS_MAX_YEARS, 10);
  const yearsToSubtract = isNaN(maxYears) || maxYears <= 0 ? 1 : maxYears;
  
  const startDate = new Date(today);
  startDate.setFullYear(today.getFullYear() - yearsToSubtract);
  startDate.setDate(today.getDate() - 1);

  const startStr = startDate.getFullYear() + '-' + 
                   String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(startDate.getDate()).padStart(2, '0');
  
  const endStr = today.getFullYear() + '-' + 
                 String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                 String(today.getDate()).padStart(2, '0');
  return {
    start: startStr,
    end: endStr
  };
}

export function createListItemRequests(id: string, siteUrl: string, listId: string): ISPOListItemRequest[] {
  if (!siteUrl || !listId) return [];

  const dateRange = getRollingYearDateRange();
  const dateFilter = `Modified ge '${dateRange.start}T00:00:00Z' and Modified le '${dateRange.end}T23:59:59Z'`;
    
  return [{
      id: `${id}`,
      method: 'GET',
      url: `${siteUrl}/_api/web/lists('${listId}')/items?$top=${SPO_LIST_ITEMS_SEARCH_SIZE}&$filter=${dateFilter}&$select=*,EncodedAbsUrl`,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
  }];
}